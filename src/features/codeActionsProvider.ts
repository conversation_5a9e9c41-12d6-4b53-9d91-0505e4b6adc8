"use strict";

import {
  Can<PERSON>ationToken,
  CodeActionContext,
  CodeActionProvider,
  CodeAction,
  TextDocument,
  Range,
  Selection
} from "vscode";
import LogtalkTerminal from "./logtalkTerminal";
import { Utils } from "../utils/utils";
import * as path from "path";
import * as fs from "fs";
import * as fsp from "fs/promises";

export class LogtalkCodeActionsProvider implements CodeActionProvider {
  public async provideCodeActions(
    document: TextDocument, range: Range | Selection,
    context: CodeActionContext, token: CancellationToken):
    Promise<CodeAction[]> {
      return null;
    }
}
