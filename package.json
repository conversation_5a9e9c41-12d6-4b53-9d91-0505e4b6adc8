{"name": "logtalk-for-vscode", "displayName": "Logtalk for VSCode", "description": "Logtalk programming support", "version": "0.42.0", "publisher": "LogtalkDotOrg", "icon": "images/logtalk.png", "license": "MIT", "engines": {"vscode": "^1.90.0"}, "categories": ["Programming Languages", "Snippets", "Linters"], "keywords": ["logtalk", "prolog", "linter", "snippet"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/pmoura"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/jacobfriedman"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/arthwang"}], "repository": {"type": "git", "url": "https://github.com/LogtalkDotOrg/logtalk-for-vscode"}, "bugs": {"url": "https://github.com/LogtalkDotOrg/logtalk-for-vscode/issues"}, "sponsor": {"url": "https://github.com/sponsors/pmoura"}, "main": "./out/src/extension", "activationEvents": ["onLanguage:logtalk", "onDebug"], "contributes": {"languages": [{"id": "logtalk", "aliases": ["Logtalk"], "configuration": "./logtalk.configuration.json", "extensions": [".lgt", ".logtalk"], "icon": {"light": "./images/logtalk-light.svg", "dark": "./images/logtalk-dark.svg"}}], "grammars": [{"language": "logtalk", "scopeName": "source.logtalk", "path": "./syntaxes/Logtalk.tmLanguage"}], "snippets": [{"language": "logtalk", "path": "./snippets/logtalk.json"}], "commands": [{"command": "logtalk.load.directory", "title": "Logtalk: Load Directory"}, {"command": "logtalk.load.file", "title": "Logtalk: Load File"}, {"command": "logtalk.make.reload", "title": "Logtalk: Make - Reload"}, {"command": "logtalk.make.optimal", "title": "Logtalk: Make - Optimal"}, {"command": "logtalk.make.normal", "title": "Logtalk: Make - Normal"}, {"command": "logtalk.make.debug", "title": "Logtalk: Make - Debug"}, {"command": "logtalk.make.check", "title": "Logtalk: Make - Check"}, {"command": "logtalk.make.circular", "title": "Logtalk: Make - Circular"}, {"command": "logtalk.make.clean", "title": "Logtalk: Make - Clean"}, {"command": "logtalk.make.caches", "title": "Logtalk: <PERSON> <PERSON> <PERSON><PERSON><PERSON>"}, {"command": "logtalk.run.tests", "title": "Logtalk: Run Tests"}, {"command": "logtalk.scan.deadCode", "title": "Logtalk: <PERSON><PERSON>"}, {"command": "logtalk.run.doclet", "title": "Logtalk: <PERSON>"}, {"command": "logtalk.generate.documentation", "title": "Logtalk: Generate Documentation"}, {"command": "logtalk.generate.diagrams", "title": "Logtalk: Generate Diagrams"}, {"command": "logtalk.open.notebook", "title": "Logtalk: Open as a Notebook"}, {"command": "logtalk.open.paired.notebook", "title": "Logtalk: Open as a Paired Notebook"}, {"command": "logtalk.sync.notebook", "title": "Logtalk: Sync paired Notebook Representations"}, {"command": "logtalk.create.project", "title": "Logtalk: Create Project"}, {"command": "logtalk.load.project", "title": "Logtalk: Load Project"}, {"command": "logtalk.open", "title": "Logtalk: <PERSON> Logtalk"}, {"command": "logtalk.rscan.deadCode", "title": "Logtalk: Scan Project Dead Code"}, {"command": "logtalk.rgenerate.documentation", "title": "Logtalk: Generate Project Documentation"}, {"command": "logtalk.rgenerate.diagrams", "title": "Logtalk: Generate Project Diagrams"}, {"command": "logtalk.run.testers", "title": "Logtalk: Run Project Testers"}, {"command": "logtalk.run.doclets", "title": "Logtalk: Run Project Doclets"}, {"command": "logtalk.open.parentFile", "title": "Logtalk: Open Parent File"}, {"command": "logtalk.toggle.codeLens", "title": "Logtalk: Toggle Code Lens"}, {"command": "logtalk.compute.metrics", "title": "Logtalk: Compute Metrics"}, {"command": "logtalk.test.documentation", "title": "Logtalk: Test Documentation Cache"}, {"command": "logtalk.logging.show", "title": "Logtalk: Show Extension Log"}, {"command": "logtalk.logging.setLevel", "title": "Logtalk: Set Extension Logging Level"}, {"command": "logtalk.refresh.documentation", "title": "Logtalk: Refresh Documentation Cache"}], "keybindings": [], "menus": {"editor/context": [{"submenu": "<PERSON><PERSON><PERSON><PERSON>", "when": "resourceLangId == logtalk"}, {"submenu": "jupytersubmenu", "when": "(resourceLangId == logtalk || resourceLangId == markdown || resourceExtname == .ipynb) && logtalk.jupytext.available == true"}, {"command": "logtalk.load.directory", "when": "resourceLangId == logtalk"}, {"command": "logtalk.load.file", "when": "resourceLangId == logtalk"}, {"command": "logtalk.run.tests", "when": "resourceLangId == logtalk"}, {"command": "logtalk.run.doclet", "when": "resourceLangId == logtalk"}, {"command": "logtalk.generate.documentation", "when": "resourceLangId == logtalk"}, {"command": "logtalk.generate.diagrams", "when": "resourceLangId == logtalk"}, {"command": "logtalk.scan.deadCode", "when": "resourceLangId == logtalk"}, {"command": "logtalk.open.parentFile", "when": "resourceLangId == logtalk"}, {"command": "logtalk.toggle.codeLens", "when": "resourceLangId == logtalk"}, {"command": "logtalk.compute.metrics", "when": "resourceLangId == logtalk"}], "explorer/context": [{"submenu": "jupytersubmenu", "when": "(resourceLangId == logtalk || resourceLangId == markdown || resourceExtname == .ipynb) && logtalk.jupytext.available == true"}, {"command": "logtalk.create.project", "when": "resourceLangId == logtalk"}, {"command": "logtalk.load.project", "when": "resourceLangId == logtalk"}, {"command": "logtalk.open", "when": "resourceLangId == logtalk"}, {"command": "logtalk.rscan.deadCode", "when": "resourceLangId == logtalk"}, {"command": "logtalk.rgenerate.diagrams", "when": "resourceLangId == logtalk"}, {"command": "logtalk.rgenerate.documentation", "when": "resourceLangId == logtalk"}, {"command": "logtalk.run.testers", "when": "resourceLangId == logtalk"}, {"command": "logtalk.run.doclets", "when": "resourceLangId == logtalk"}], "makesubmenu": [{"command": "logtalk.make.reload", "when": "resourceLangId == logtalk"}, {"command": "logtalk.make.optimal", "when": "resourceLangId == logtalk"}, {"command": "logtalk.make.normal", "when": "resourceLangId == logtalk"}, {"command": "logtalk.make.debug", "when": "resourceLangId == logtalk"}, {"command": "logtalk.make.check", "when": "resourceLangId == logtalk"}, {"command": "logtalk.make.circular", "when": "resourceLangId == logtalk"}, {"command": "logtalk.make.clean", "when": "resourceLangId == logtalk"}, {"command": "logtalk.make.caches", "when": "resourceLangId == logtalk"}], "jupytersubmenu": [{"command": "logtalk.open.notebook", "when": "(resourceLangId == logtalk || resourceLangId == markdown) && logtalk.jupytext.available == true", "group": "jupyter@1"}, {"command": "logtalk.open.paired.notebook", "when": "(resourceLangId == logtalk || resourceLangId == markdown) && logtalk.jupytext.available == true", "group": "jupyter@2"}, {"command": "logtalk.sync.notebook", "when": "(resourceLangId == logtalk || resourceLangId == markdown || resourceExtname == .ipynb) && logtalk.jupytext.available == true", "group": "jupyter@3"}]}, "submenus": [{"label": "Logtalk: Make", "id": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Logtalk: <PERSON><PERSON><PERSON>", "id": "jupytersubmenu"}], "breakpoints": [{"language": "logtalk"}], "debuggers": [{"type": "logtalk", "label": "Logtalk Debug", "languages": ["logtalk"]}], "chatParticipants": [{"id": "logtalk-for-vscode.logtalk", "name": "logtalk", "fullName": "Logtalk", "description": "Ask questions about the Logtalk programming language", "isSticky": true, "commands": [{"name": "handbook", "description": "Search the Logtalk Handbook for information"}, {"name": "apis", "description": "Search the Logtalk APIs documentation for information"}, {"name": "examples", "description": "Get help with Logtalk code examples and patterns"}]}], "configuration": {"title": "", "properties": {"logtalk.home.path": {"type": "string", "default": "", "description": "Logtalk home absolute path (LOGTALKHOME environment variable value). Required.", "order": 1}, "logtalk.user.path": {"type": "string", "default": "", "description": "Logtalk user absolute path (LOGTALKUSER environment variable value). Required.", "order": 2}, "logtalk.backend": {"type": "string", "default": "", "enum": ["b", "ciao", "cx", "eclipse", "gnu", "ji", "sic<PERSON>us", "swi", "tau", "trealla", "xsb", "xvm", "yap"], "description": "Logtalk backend. Required.", "order": 3}, "logtalk.executable.path": {"type": "string", "default": "", "description": "Logtalk integration script absolute path. Optional.", "order": 4}, "logtalk.executable.arguments": {"type": "array", "description": "Arguments of the Logtalk integration script. Optional.", "default": [], "order": 5}, "logtalk.tester.script": {"type": "string", "default": "/usr/local/bin/logtalk_tester", "description": "Automation script for running tests. Optional.", "order": 6}, "logtalk.tester.arguments": {"type": "array", "description": "Arguments for the automation script for running tests. Must include the backend identifier. Optional.", "default": [], "order": 7}, "logtalk.doclet.script": {"type": "string", "default": "/usr/local/bin/logtalk_doclet", "description": "Automation script for running doclets. Optional.", "order": 8}, "logtalk.doclet.arguments": {"type": "array", "description": "Arguments for the automation script for running doclets. Must include the backend identifier. Optional.", "default": [], "order": 9}, "logtalk.documentation.script": {"type": "string", "default": "/usr/local/bin/lgt2html", "description": "Script for converting the XML files generated by the Logtalk lgtdoc tool to their final format. Optional.", "order": 10}, "logtalk.documentation.arguments": {"type": "array", "description": "Arguments for the script that converts the XML files generated by the Logtalk lgtdoc tool. Optional.", "default": [], "order": 11}, "logtalk.diagrams.script": {"type": "string", "default": "/usr/local/bin/lgt2svg", "description": "Script for converting the .d2 and .dot files generated by the Logtalk diagrams tool. Optional.", "order": 13}, "logtalk.diagrams.arguments": {"type": "array", "description": "Arguments for the script that converts the .d2 and .dot files generated by the Logtalk diagrams tool. Optional.", "default": [], "order": 14}, "logtalk.scripts.timeout": {"type": "number", "description": "Timeout (in milliseconds) for waiting to run the scripts that convert documentation and diagram files to final formats when running the lgtdoc and diagrams tools. Also for waiting to populate the 'Problems' pane when compiling a file.", "default": 480000, "order": 15}, "logtalk.enableCodeLens": {"type": "boolean", "description": "Enables testing and code metrics results to be displayed using CodeLens.", "default": true, "order": 16}, "logtalk.jupytext.path": {"type": "string", "description": "Absolute path to the jupytext executable. Can also be a Python interpreter call running the jupytext module. Optional.", "default": "python3 -m jupytext", "order": 17}, "logtalk.logging.level": {"type": "string", "description": "Controls the verbosity of logging output for the Logtalk extension, especially useful for debugging the chat participant.", "default": "warn", "enum": ["off", "error", "warn", "info", "debug"], "enumDescriptions": ["No logging output", "Only error messages", "Error and warning messages", "Error, warning, and informational messages", "All messages including detailed debug information"], "order": 18}}}, "configurationDefaults": {"[logtalk]": {"editor.tabSize": 4, "editor.insertSpaces": false, "editor.wordWrap": "on"}}, "walkthroughs": [{"id": "logtalk-walkthrough", "title": "Get started with Logtalk development", "description": "Your first steps customizing your development environment and creating your first project", "steps": [{"id": "configure", "title": "Extension configuration", "description": "The extension **must** be configured before it can be used. Open the settings to enter your configuration by following the instructions in the \"Configuration\" section in the readme file.\n[Open Settings](command:logtalk-for-vscode.openSettings)\n[Open Readme](command:logtalk-for-vscode.openReadme)", "media": {"markdown": "media/settings.md"}, "completionEvents": ["onCommand:logtalk-for-vscode.openSettings", "onCommand:logtalk-for-vscode.openReadme"]}, {"id": "open", "title": "Open an example", "description": "To check that the extension is configured correctly, start by opening one of the examples from the Logtalk distribution by navigating to your ``LOGTALKUSER`` directory.\n[Open Example](command:logtalk-for-vscode.openExample)", "media": {"image": "media/open_example.png", "altText": "File/folder open dialog."}, "completionEvents": ["onCommand:logtalk-for-vscode.openExample"]}, {"id": "load", "title": "Compile and load the example", "description": "Next, compile and load the example by opening its loader file and selecting the \"Logtalk: Load File\" command from the context menu or by opening one of the example source files selecting the \"Logtalk: Load Directory\" command from the context menu.", "media": {"image": "media/load_file.png", "altText": "Context menu with the load file command selected."}, "completionEvents": ["onCommand:logtalk.load.directory", "onCommand:logtalk.load.file"]}, {"id": "test", "title": "Run the example tests.", "description": "Run the example tests by selecting the \"Logtalk: Run Tests\" command from the context menu.", "media": {"image": "media/run_tests.png", "altText": "Context menu with the run tests command selected."}, "completionEvents": ["onCommand:logtalk.run.tests"]}, {"id": "create", "title": "Create a project.", "description": "Create a project using the template files.\n[Create Project](command:logtalk.create.project)", "media": {"markdown": "media/create.md"}, "completionEvents": ["onCommand:logtalk.create.project"]}]}]}, "scripts": {"vscode:prepublish": "tsc -p ./", "compile": "tsc -watch -p ./", "test": "tsc ./tests/runTest.ts", "vsix:make": "vsce package --baseImagesUrl https://raw.githubusercontent.com/llvm/llvm-project/master/clang-tools-extra/clangd/clients/clangd-vscode/", "vsix:install": "code --install-extension logtalk-for-vscode-0.42.0.vsix"}, "devDependencies": {"@types/bluebird": "^3.5.38", "@types/mocha": "^2.2.48", "@types/node": "^20.4.2", "@vscode/test-electron": "^1.6.2", "@vscode/vsce": "^3.5.0", "@xmldom/xmldom": "^0.8.9", "glob": "^7.1.7", "html2commonmark": "^0.6.1", "typescript": "^5.1.6"}, "dependencies": {"@types/vscode": "^1.90.0", "@vscode/debugprotocol": "^1.33.0", "@xmldom/xmldom": "^0.8.9", "child-process-promise": "^2.2.1", "fs-extra-plus": "^0.1.3", "fuse.js": "^6.6.2", "jsesc": "^2.5.2", "mocha": "^10.2.0", "path": "^0.12.7", "process-promises": "^1.0.0", "semver": "^7.7.1", "split": "^1.0.1", "stack-trace": "0.0.10", "xpath": "0.0.24"}}